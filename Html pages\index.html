<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>With Love - Handmade Gifts</title>
    <link rel="stylesheet" href="../Style/styles.css" />
    <link rel="stylesheet" href="../Style/products.css" />
    <link rel="stylesheet" href="../Style/shop-index-layout.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <link rel="stylesheet" href="../Style/modal.css" />
    <link rel="stylesheet" href="../Style/loader.css" />
    <!-- Favicons -->
    <link
      rel="icon"
      type="image/png"
      href="../images/logo.png?ver=1.0"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="../images/logo.png?ver=1.0"
      sizes="16x16"
    />
    <link rel="shortcut icon" href="../images/logo.png?ver=1.0" />
    <!-- Apple Touch Icon (for iOS devices) -->
    <link rel="apple-touch-icon" href="../images/logo.png" />
  </head>
  <body>
    <div class="loader">
      <div class="loader-content">
        <div class="loader-spinner">
          <div class="spinner-circle"></div>
          <div class="spinner-circle"></div>
          <div class="spinner-circle"></div>
        </div>
        <div class="loading-text">with love</div>
      </div>
    </div>
    <div class="container">
      <header>
        <div class="logo">
          <a href="../Html pages/index.html">
            <img src="../images/logo.png" alt="With Love Logo" />
          </a>
        </div>
        <div class="title">With Love</div>
        <div class="header-right">
          <div class="search">
            <button
              type="button"
              class="search-toggle"
              id="searchToggle"
              aria-label="Search"
            >
              Search
            </button>
            <div class="search-container" id="searchContainer">
              <div class="search-input-wrapper">
                <input
                  type="text"
                  id="searchInput"
                  class="search-input"
                  placeholder="Search for products..."
                  autocomplete="off"
                />
                <i class="fas fa-search search-icon"></i>
                <div id="searchSuggestions" class="search-suggestions"></div>
              </div>
            </div>
          </div>
          <div class="header-icons">
            <a
              href="../Html pages/wishlist.html"
              class="wishlist-icon"
              aria-label="Wishlist"
            >
              <i class="far fa-heart"></i>
              <span class="icon-badge wishlist-count">0</span>
            </a>
            <a
              href="../Html pages/cart.html"
              class="cart-icon"
              aria-label="Shopping Cart"
            >
              <i class="fas fa-shopping-cart"></i>
              <span class="icon-badge cart-count">0</span>
            </a>
          </div>
          <div class="hamburger" id="hamburger">
            <span class="bar"></span>
            <span class="bar"></span>
            <span class="bar"></span>
          </div>
        </div>
        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="../Html pages/index.html">Home</a></li>
            <li><a href="../Html pages/about.html">About</a></li>
            <li><a href="../Html pages/shop.html">Shop</a></li>
            <li><a href="../Html pages/contact.html">Contact</a></li>
            <li><a href="../Html pages/login.html">Login</a></li>
          </ul>
        </nav>
      </header>
      <!--main section of web page-->
      <main>
        <section class="hero">
          <div class="letter-image">
            <div class="animated-mail">
              <div class="heart">❤</div>
              <div class="back-fold"></div>
              <div class="letter">
                <div class="letter-border"></div>
                <div class="letter-content">
                  <h3>Hello There!</h3>
                  <p>Welcome to our world of handmade treasures.</p>
                  <p>Each piece is crafted with love and care.</p>
                  <p>Enjoy exploring!</p>
                  <p>❤️ With Love</p>
                </div>
                <div class="letter-stamp">
                  <div class="letter-stamp-inner"></div>
                </div>
              </div>
              <div class="top-fold"></div>
              <div class="body"></div>
              <div class="left-fold"></div>
              <div class="right-fold"></div>
            </div>
          </div>
        </section>

        <section class="products">
          <h2>View Our Products</h2>
          <div class="product-grid" id="featuredProductsGrid">
            <!-- Products will be loaded here dynamically -->
          </div>
        </section>
      </main>

      <footer>
        <div class="contact">
          <h3>Contact Us</h3>
          <p>
            <a
              href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>"
              target="_blank"
              rel="noopener noreferrer"
              ><i class="fas fa-envelope"></i> <EMAIL></a
            >
          </p>
          <p>
            <a href="tel:+35679652171"
              ><i class="fas fa-phone-alt"></i> +356 79652171</a
            >
          </p>
        </div>
        <div class="social">
          <h3>Follow Us</h3>
          <div class="social-icons">
            <a
              href="https://www.facebook.com"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Facebook"
              ><i class="fab fa-facebook-f"></i
            ></a>
            <a
              href="https://www.instagram.com"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Instagram"
              ><i class="fab fa-instagram"></i
            ></a>
            <a
              href="https://www.messenger.com"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Messenger"
              ><i class="fab fa-facebook-messenger"></i
            ></a>
          </div>
        </div>
        <div class="newsletter">
          <h3>Newsletter</h3>
          <form id="newsletterForm" novalidate>
            <input
              type="hidden"
              name="_subject"
              value="New newsletter subscription"
            />
            <input type="hidden" name="_template" value="table" />
            <input type="email" name="email" placeholder="Your email" />
            <button type="submit">Subscribe</button>
          </form>
        </div>
      </footer>
    </div>
    <script type="module">
      import { products } from "../js/products.js";

      // Global variables
      let productGrid;
      let wishlist = [];

      // Main initialization function for index page
      async function initFeaturedProducts() {
        productGrid = document.querySelector("#featuredProductsGrid");
        if (!productGrid) {
          console.error("Featured products grid not found");
          return;
        }

        // Wait for authUtils to be available and load wishlist
        await initializeWishlistForIndex();

        // Display featured products (first 3 products for homepage)
        const featuredProducts = products.slice(0, 3);
        displayFeaturedProducts(featuredProducts);
      }

      // Initialize wishlist for index page
      async function initializeWishlistForIndex() {
        let attempts = 0;
        while (!window.authUtils && attempts < 50) {
          await new Promise((resolve) => setTimeout(resolve, 100));
          attempts++;
        }

        const authUtils = window.authUtils;
        if (authUtils && authUtils.isLoggedIn()) {
          // Load wishlist from server for authenticated users
          try {
            const response = await authUtils.makeAuthenticatedRequest(
              "/api/wishlist"
            );
            if (response.ok) {
              const data = await response.json();
              if (data.success) {
                wishlist = data.wishlist;
              }
            }
          } catch (error) {
            console.error("Error loading wishlist:", error);
            wishlist = JSON.parse(localStorage.getItem("wishlist") || "[]");
          }
        } else {
          // Load from localStorage for non-authenticated users (fallback)
          wishlist = JSON.parse(localStorage.getItem("wishlist") || "[]");
        }
      }

      function displayFeaturedProducts(productsToShow) {
        console.log("Displaying featured products:", productsToShow);
        if (!productGrid) {
          console.error("Product grid element not found");
          return;
        }

        if (!productsToShow || productsToShow.length === 0) {
          productGrid.innerHTML =
            '<p class="no-products">No featured products found.</p>';
          return;
        }

        productGrid.innerHTML = productsToShow
          .map(
            (product) => `
                <div class="product-card" data-product-id="${product.id}">
                    <div class="product-image">
                        <img src="${product.image}" alt="${
              product.name
            }" loading="lazy" onerror="this.src='../images/placeholder.jpg'">
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">${product.name}</h3>
                        <div class="product-price">
                            <span class="price-amount">€${product.price.toFixed(
                              2
                            )}</span>
                        </div>
                        <p class="product-template-note">Preview template from our collection</p>
                        <div class="product-actions">
                            <button class="btn btn-view" onclick="showProductPreview(${
                              product.id
                            })">
                                <span>View</span>
                            </button>
                            <div class="action-buttons">
                                <button class="btn btn-wishlist ${
                                  wishlist && wishlist.includes(product.id)
                                    ? "active"
                                    : ""
                                }" 
                                        data-product-id="${product.id}" 
                                        onclick="event.stopPropagation(); if(window.toggleWishlist) { toggleWishlist(this, ${
                                          product.id
                                        }); }" 
                                        aria-label="Add to wishlist">
                                    <i class="${
                                      wishlist && wishlist.includes(product.id)
                                        ? "fas"
                                        : "far"
                                    } fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `
          )
          .join("");

        // Apply dynamic grid class based on number of products
        updateGridLayout(productsToShow.length);
      }

      function updateGridLayout(productCount) {
        if (!productGrid) return;

        // Remove all existing grid classes
        productGrid.classList.remove(
          "items-1",
          "items-2",
          "items-3",
          "items-4",
          "items-5-plus"
        );

        // Add appropriate class based on product count
        if (productCount === 1) {
          productGrid.classList.add("items-1");
        } else if (productCount === 2) {
          productGrid.classList.add("items-2");
        } else if (productCount === 3) {
          productGrid.classList.add("items-3");
        } else if (productCount === 4) {
          productGrid.classList.add("items-4");
        } else if (productCount >= 5) {
          productGrid.classList.add("items-5-plus");
        }
      }

      // Global functions for preview - redirect to full page
      window.showProductPreview = function (productId) {
        // Redirect to the product preview page with the product ID
        window.location.href = `../Html pages/product-preview.html?id=${productId}`;
      };

      // Initialize featured products when the DOM is loaded
      console.log("Index page script loaded");

      // Initialize when the DOM is fully loaded
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", function () {
          console.log("DOM fully loaded, initializing featured products...");
          initFeaturedProducts().catch((error) => {
            console.error("Error initializing featured products:", error);
          });
        });
      } else {
        // DOM is already loaded, initialize immediately
        console.log("DOM already loaded, initializing featured products...");
        initFeaturedProducts().catch((error) => {
          console.error("Error initializing featured products:", error);
        });
      }
    </script>
    <script src="../js/script.js"></script>
    <script src="../js/cart.js"></script>
    <script src="../js/auth-utils.js"></script>
    <script type="module" src="../js/search-animated.js"></script>
    <script type="module" src="../js/subscribe.js"></script>
    <script type="module" src="../js/shop.js"></script>
    <script src="https://cdn.jotfor.ms/agent/embedjs/01978832ad6d718987e4ef7f6ec870f11a69/embed.js?skipWelcome=1&maximizable=1"></script>
    <script src="../Scripts/loader.js" defer></script>
    <script src="../js/wishlist.js"></script>
  </body>
</html>
